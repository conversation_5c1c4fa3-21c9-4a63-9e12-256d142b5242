/**
 * @returns {String} the string with all zeroes contained in a <span>
 */
export declare function spanAllZeroes(s: string): string;
/**
 * @returns {String} the string with each character contained in a <span>
 */
export declare function spanAll(s: string, offset?: number): string;
/**
 * @returns {String} the string with leading zeroes contained in a <span>
 */
export declare function spanLeadingZeroes(address: string): string;
/**
 * Groups an address
 * @returns {String} a grouped address
 */
export declare function simpleGroup(addressString: string, offset?: number): string[];
//# sourceMappingURL=helpers.d.ts.map