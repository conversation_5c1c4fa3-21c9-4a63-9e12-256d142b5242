{
  "compilerOptions": {
    "incremental": true,
    "target": "es2022",
    "module": "commonjs",
    "lib": ["dom", "es2022"],
    "jsx": "react-jsx",
    "strict": true,
    "sourceMap": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "strictPropertyInitialization": false, // 关闭严格属性初始化
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "allowJs": true,
    "outDir": ".erb/dll"
  },
  "exclude": ["test", "release/build", "release/app/dist", ".erb/dll"]
}
