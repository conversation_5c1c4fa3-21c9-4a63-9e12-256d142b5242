import OpenAI from 'openai';

import <PERSON><PERSON><PERSON>, { ErnieAIOptions } from './ernie';
import <PERSON><PERSON><PERSON>, { GeminiAIOptions } from './gemini';
import HunYuanA<PERSON>, { HunYuanAIOptions } from './hunyuan';
import <PERSON>maxAI, { MinimaxAIOptions } from './minimax';
import QWen<PERSON><PERSON>, { QWenAIOptions } from './qwen';
import Spark<PERSON><PERSON>, { SparkAIOptions } from './spark';
import VYroA<PERSON>, { VYroAIOptions } from './vyro';
import DifyA<PERSON>, { DifyAIOptions } from './dify';

export {
  ErnieA<PERSON>,
  type ErnieAIOptions,
  GeminiAI,
  type GeminiAIOptions,
  HunYuanAI,
  type HunYuanAIOptions,
  MinimaxAI,
  type MinimaxAIOptions,
  OpenAI,
  QWenAI,
  type QWenAIOptions,
  SparkAI,
  type SparkAIOptions,
  VYroAI,
  type VYroAIOptions,
  DifyAI,
  type DifyAIOptions,
};

export {
  OpenAIError,
  APIError,
  APIConnectionError,
  APIConnectionTimeoutError,
  APIUserAbortError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  BadRequestError,
  AuthenticationError,
  InternalServerError,
  PermissionDeniedError,
  UnprocessableEntityError,
} from 'openai';

export * from './resource';
export * from './streaming';
export * from './util';

export default {
  version: process.env.PKG_VERSION,
};
