{"name": "chatgpt-on-cs", "description": "多平台智能客服，允许使用 ChatGPT 作为客服机器人", "version": "1.4.5", "keywords": ["electron", "boilerplate", "react", "typescript", "ts", "sass", "webpack", "hot", "reload"], "bugs": {"url": "https://github.com/lrhh123/ChatGPT-On-CS"}, "repository": {"type": "git", "url": "git+https://github.com/lrhh123/ChatGPT-On-CS.git"}, "license": "AGPL-3.0", "contributors": [{"name": "lrhh123", "email": "<EMAIL>", "url": "https://github.com/lrhh123"}], "main": "./src/main/main.ts", "scripts": {"build": "npx concurrently \"npm run build:main\" \"npm run build:renderer\"", "build:dll": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.renderer.dev.dll.ts", "build:main": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.main.prod.ts", "build:renderer": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.renderer.prod.ts", "postinstall": "npx ts-node .erb/scripts/check-native-dep.js && npx electron-builder install-app-deps && npm run build:dll", "lint": "cross-env NODE_ENV=development eslint . --ext .js,.jsx,.ts,.tsx", "package": "npx ts-node ./.erb/scripts/clean.js dist && npm run build && npx electron-builder build --publish never && npm run build:dll", "rebuild": "electron-rebuild --parallel --types prod,dev,optional --module-dir release/app", "start": "npx ts-node ./.erb/scripts/check-port-in-use.js && npm run start:renderer", "start:main": "cross-env NODE_ENV=development electronmon -r ts-node/register/transpile-only .", "start:preload": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.preload.dev.ts", "start:renderer": "npx cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true npx webpack serve --config ./.erb/configs/webpack.config.renderer.dev.ts", "test": "jest"}, "browserslist": [], "prettier": {"singleQuote": true, "overrides": [{"files": [".prettier<PERSON>", ".eslintrc"], "options": {"parser": "json"}}]}, "jest": {"testTimeout": 300000, "moduleDirectories": ["node_modules", "release/app/node_modules", "src"], "moduleFileExtensions": ["js", "jsx", "ts", "tsx", "json"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/.erb/mocks/fileMock.js", "\\.(css|less|sass|scss)$": "identity-obj-proxy"}, "setupFiles": ["./.erb/scripts/check-build-exists.ts"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost/"}, "testPathIgnorePatterns": ["release/app/dist", ".erb/dll"], "transform": {"\\.(ts|tsx|js|jsx)$": "ts-jest"}}, "dependencies": {"@chakra-ui/anatomy": "^2.2.2", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@monaco-editor/react": "^4.6.0", "@tanstack/react-query": "4.36.1", "@vercel/analytics": "^1.3.1", "ansi-styles": "^6.2.1", "axios": "^1.6.7", "body-parser": "^1.20.2", "cors": "^2.8.5", "debug": "^4.3.4", "dottie": "^2.0.6", "electron-debug": "^3.2.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.4", "emoji-picker-react": "^4.10.0", "exceljs": "^4.4.0", "express": "^4.19.2", "express-async-handler": "^1.2.0", "framer-motion": "^11.0.3", "immer": "^10.0.3", "inflection": "^3.0.0", "lodash": "^4.17.21", "lru-cache": "^7.18.3", "luxon": "^3.4.4", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "monaco-editor": "^0.49.0", "ms": "^2.1.3", "net": "^1.0.2", "node-cron": "^3.0.3", "openai": "^4.38.3", "pg-connection-string": "^2.6.4", "pg-hstore": "^2.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "react-paginate": "^8.2.0", "react-router-dom": "^6.16.0", "react-spinners": "^0.13.8", "react-table": "^7.8.0", "react-use-websocket": "^4.8.1", "reflect-metadata": "^0.2.2", "remark-gfm": "^4.0.0", "retry-as-promised": "^7.0.4", "semver": "^7.6.0", "sequelize": "^6.37.3", "sequelize-pool": "^8.0.0", "socket.io": "^4.7.5", "source-map-support": "^0.5.21", "toposort-class": "^1.0.1", "tslib": "^2.6.2", "uuid": "^9.0.1", "validator": "^13.11.0", "wkx": "^0.5.0", "zustand": "^4.5.0"}, "devDependencies": {"@electron/notarize": "^2.1.0", "@electron/rebuild": "^3.3.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@svgr/webpack": "^8.1.0", "@teamsupercell/typings-for-css-modules-loader": "^2.5.2", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^14.0.0", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.5", "@types/lodash": "^4.17.1", "@types/luxon": "^3.4.2", "@types/node": "20.6.2", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/react-table": "^7.7.20", "@types/react-test-renderer": "^18.0.1", "@types/source-map-support": "^0.5.10", "@types/terser-webpack-plugin": "^5.0.4", "@types/webpack-bundle-analyzer": "^4.6.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "app-builder-lib": "25.0.0-alpha.6", "browserslist-config-erb": "^0.0.3", "chalk": "^4.1.2", "concurrently": "^8.2.1", "core-js": "^3.32.2", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "detect-port": "^1.5.1", "dotenv": "^16.4.4", "electron": "^26.6.10", "electron-builder": "^24.13.3", "electron-devtools-installer": "^3.2.0", "electron-rebuild": "^3.2.9", "electronmon": "^2.0.2", "eslint": "^8.49.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-erb": "^4.1.0-0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-import-resolver-webpack": "^0.13.7", "eslint-plugin-compat": "^4.2.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.4.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "node-addon-api": "^8.0.0", "prettier": "^3.0.3", "prettier-eslint": "^16.3.0", "react-refresh": "^0.14.0", "react-test-renderer": "^18.2.0", "rimraf": "^5.0.1", "sass": "^1.67.0", "sass-loader": "^13.3.2", "sqlite3": "^5.1.7", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typeorm": "^0.3.20", "typescript": "^5.2.2", "url-loader": "^4.1.1", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.9.0"}, "engines": {"node": ">=14.0.0", "npm": ">=7.0.0"}, "electronmon": {"patterns": ["!**/**", "src/main/**"], "logLevel": "quiet"}}