{"files.associations": {".eslintrc": "jsonc", ".prettierrc": "jsonc", ".eslintignore": "ignore"}, "eslint.validate": ["javascript", "javascriptreact", "html", "typescriptreact"], "javascript.validate.enable": false, "javascript.format.enable": false, "typescript.format.enable": false, "search.exclude": {".git": true, ".eslintcache": true, ".erb/dll": true, "release/{build,app/dist}": true, "node_modules": true, "npm-debug.log.*": true, "test/**/__snapshots__": true, "package-lock.json": true, "*.{css,sass,scss}.d.ts": true}, "cSpell.words": ["dify", "jin<PERSON><PERSON><PERSON>"]}