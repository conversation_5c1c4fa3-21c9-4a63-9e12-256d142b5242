module.exports = {
  extends: 'erb',
  plugins: ['@typescript-eslint'],
  rules: {
    // A temporary hack related to IDE not resolving correct package.json
    'import/no-extraneous-dependencies': 'off',
    'react/react-in-jsx-scope': 'off',
    'react/jsx-filename-extension': 'off',
    'import/extensions': 'off',
    'import/no-unresolved': 'off',
    'import/no-import-module-exports': 'off',
    'no-shadow': 'off',
    '@typescript-eslint/no-shadow': 'error',
    'no-unused-vars': 'off',
    'react/function-component-definition': 'off',
    'react/jsx-curly-brace-presence': 'off',
    'react/require-default-props': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/destructuring-assignment': 'off',
    'import/no-named-as-default': 'off',
    'prefer-promise-reject-errors': 'off',
    'react/jsx-no-useless-fragment': 'off',
    'no-promise-executor-return': 'off',
    'import/prefer-default-export': 'off',
    'promise/no-promise-in-callback': 'off',
    'react/no-array-index-key': 'off',
    'class-methods-use-this': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'no-console': 'off',
    'max-classes-per-file': 'off',
    'no-continue': 'off',
    'no-plusplus': 'off',
    'no-underscore-dangle': 'off',
    camelcase: 'off',
    'no-use-before-define': 'off',
    'no-dupe-class-members': 'off',
  },
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
  },
  settings: {
    'import/resolver': {
      // See https://github.com/benmosher/eslint-plugin-import/issues/1396#issuecomment-575727774 for line below
      node: {},
      webpack: {
        config: require.resolve('./.erb/configs/webpack.config.eslint.ts'),
      },
      typescript: {},
    },
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx'],
    },
  },
};
