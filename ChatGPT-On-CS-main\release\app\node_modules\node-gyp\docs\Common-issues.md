## Python Issues OSX

Make sure you are using the native Python version in OSX.  If you use a MacPorts of HomeBrew version, you may run into problems.

If you have issues with `execvp`, be sure to check your `$PYTHON` environment variable.  If it is not set to the native version, unset it and try again.

Notes: https://gist.github.com/erichocean/5177582

## npm ERR! `node-gyp rebuild`(Windows)
* just install the build tools from [here](https://visualstudio.microsoft.com/thank-you-downloading-visual-studio/?sku=BuildTools)
Please note the version as is required in below command e.g **2017**
* Launch cmd, run `npm config set msvs_version 2017`
* close and open new CMD/terminal and all is well :100: 

