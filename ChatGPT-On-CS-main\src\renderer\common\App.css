/*
 * @NOTE: Prepend a `~` to css file paths that are in your node_modules
 *        See https://github.com/webpack-contrib/sass-loader#imports
 */
html,
body {
  height: 100%;
  overflow: auto; /* 或者使用 'scroll' 来强制显示滚动条 */
}

body {
  height: 100vh;
}

@font-face {
  font-family: 'zhFont';
  src: url('../../../assets/fonts/庞门正道标题体.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.font-zh {
  font-family: 'zhFont';
}

.table-tiny th,
.table-tiny td {
  padding-left: 4px;
  padding-right: 4px;
}

.table-tiny th:first-child,
.table-tiny td:first-child {
  max-width: 60px;
}

* {
  scrollbar-width: thin;
  scrollbar-color: var(--chakra-colors-gray-300) transparent;
}

*::-webkit-scrollbar {
  width: 5px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: var(--chakra-colors-gray-300);
  border-radius: 10px;
  border: none;
}
