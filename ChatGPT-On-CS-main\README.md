<div align="center">

<a href="https://doc.lazaytools.top/"><img src="./docs/logo.png" width="180" height="180" alt="lazaytools logo"></a>

## 懒人客服

<p align="center">
  <a href="./README_EN.md">English</a> |
  <a href="./README.md">简体中文</a>
</p>

懒人客服是一个基于 LLM 大语言模型的知识库的集成客服系统，提供开箱即用的智能客服解决方案，支持微信、千牛、哔哩哔哩、抖音企业号、抖音、抖店、拼多多、微博聊天、小红书专业号运营、小红书、知乎等平台接入，支持文本、语音和图片，通过插件访问操作系统和互联网等外部资源，支持基于自有知识库定制企业 AI 应用.

</div>

<p align="center">
  <a href="https://github.com/cs-lazy-tools/ChatGPT-On-CS/releases">
    <img height="21" src="./docs/立即下载-d4eaf7.svg" alt="cloud">
  </a>
  <a href="https://doc.lazaytools.top/">
    <img height="21" src="./docs/相关文档-7d09f1.svg" alt="document">
  </a>
  <a href="https://github.com/cs-lazy-tools/ChatGPT-On-CS/blob/main/BUILD.md">
    <img height="21" src="./docs/本地开发-d4eaf7.svg" alt="development">
  </a>
  <a href="https://github.com/cs-lazy-tools/ChatGPT-On-CS/blob/main/LICENSE">
    <img height="21" src="./docs/license_AGPL_3.0.svg" alt="license">
  </a>
</p>


本项目可选择 GPT3.5/GPT4.0/懒人百宝箱/FastGPT/DifyAI/通义千问/文心一言 等平台大模型，能处理文本、语音和图片，通过插件访问操作系统和互联网等外部资源，支持基于自有知识库定制企业 AI 应用。

* [GitHub] https://github.com/cs-lazy-tools/ChatGPT-On-CS
* [Gitee] https://gitee.com/alsritter/ChatGPT-On-CS · (国内用户推荐)

## 主要功能

<div align="center">

<a href="https://doc.lazaytools.top/"><img src="./docs/intro1.png" width="380" alt="lazaytools logo"></a>

</div>

- [x] 多平台支持：当前支持微信、千牛、哔哩哔哩、抖音企业号、抖音、抖店、微博聊天、小红书专业号运营、小红书、小红书千帆客户端、知乎等平台，未来将不断扩展支持更多社交媒体平台.
- [x] 预设回复内容：允许用户设置自定义回复，以应对常见问题，提高回复效率.
- [x] 接入ChatGPT接口，根据客户的咨询内容智能生成回复，适用于处理复杂或者个性化的客户咨询.
- [x] 发送图片和二进制文件：支持发送图片等二进制文件，满足多样化的客户服务需求.
- [x] 知识库： 通过上传知识库文件自定义专属机器人，可作为数字分身、智能客服、私域助手使用.
- [x] 各个平台独立的插件系统，支持插件访问操作系统和互联网等外部资源，支持基于自有知识库定制企业 AI 应用.

## 演示视频
[观看视频](https://www.bilibili.com/video/BV1qz421Q73S)

## 功能展示截图

| ![Demo](./docs/intro1.png) | ![alt text](./docs/intro2.png) |
| -------------------------- | ------------------------------ |
| ![Demo](./docs/intro3.png) | ![Demo](./docs/intro4.png)     |
| ![Demo](./docs/intro5.png) | ![alt text](./docs/intro6.png) |


## 联系我们
如果有问题需要反馈，或者对项目有什么特性希望支持的，可以添加小助手微信：

![微信扫码添加客服进群](https://image.siweigpt.com/docimage/202412031818028.png)

## 使用说明
可以参考 [使用说明](https://doc.lazaytools.top/) 文档

## 项目计划 RoadMap
- [x] 支持微信平台的基础聊天功能
- [x] 支持千牛平台的基础聊天功能
- [x] 支持京麦平台的基础聊天功能
- [x] 添加关闭自动回复，只使用关键词回复的功能
- [x] 添加延时随机时间
- [x] 优化各个平台的独立配置功能
- [x] 支持 Excel 导入回复内容
- [x] 支持导出回复内容到 Excel
- [x] 优化千牛的对接模式
- [x] 优化微信平台的回复速度
- [x] 添加中文路径的支持
- [x] 支持自动检测人类操作后自动暂停项目
- [x] 新增 wx 群聊需要使用 @ 机器人才能回复的功能
- [x] 支持浏览器多开功能
- [x] 支持导出聊天记录到 Excel
- [ ] 支持关键词匹配测试功能
- [x] 实现小红书回复后，自动私聊的功能
- [ ] 支持抖音直播平台自动回复功能
- [x] 支持拼多多
- [ ] 支持多平台文章自动发送功能
- [ ] 支持本地聊天模型的加载


## 🤝 参与贡献
我们热烈欢迎各种形式的贡献。假如你对编写代码有兴趣，不妨浏览我们的 GitHub Issues，选择一个你感兴趣的问题，尽情发挥你的创造力和技术才能，向我们展示你独特的解决方案。此外，我们也鼓励大家提出新想法、改进建议，或参与讨论，共同推动项目的发展。

<!-- Copy-paste in your Readme.md file -->

<a href="https://next.ossinsight.io/widgets/official/compose-recent-active-contributors?repo_id=768534013&limit=30" target="_blank" style="display: block" align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://next.ossinsight.io/widgets/official/compose-recent-active-contributors/thumbnail.png?repo_id=768534013&limit=30&image_size=auto&color_scheme=dark" width="655" height="auto">
    <img alt="Active Contributors of cs-lazy-tools/ChatGPT-On-CS - Last 28 days" src="https://next.ossinsight.io/widgets/official/compose-recent-active-contributors/thumbnail.png?repo_id=768534013&limit=30&image_size=auto&color_scheme=light" width="655" height="auto">
  </picture>
</a>

<!-- Made with [OSS Insight](https://ossinsight.io/) -->

<!-- Copy-paste in your Readme.md file -->

<a href="https://next.ossinsight.io/widgets/official/compose-org-participants-growth?activity=new&period=past_28_days&owner_id=169274333" target="_blank" style="display: block" align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://next.ossinsight.io/widgets/official/compose-org-participants-growth/thumbnail.png?activity=new&period=past_28_days&owner_id=169274333&image_size=4x7&color_scheme=dark" width="657" height="auto">
    <img alt="New trends of cs-lazy-tools" src="https://next.ossinsight.io/widgets/official/compose-org-participants-growth/thumbnail.png?activity=new&period=past_28_days&owner_id=169274333&image_size=4x7&color_scheme=light" width="657" height="auto">
  </picture>
</a>

<!-- Made with [OSS Insight](https://ossinsight.io/) -->

## 🌟 Star History

<a href="https://star-history.com/#cs-lazy-tools/ChatGPT-On-CS&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=cs-lazy-tools/ChatGPT-On-CS&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=cs-lazy-tools/ChatGPT-On-CS&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=cs-lazy-tools/ChatGPT-On-CS&type=Date" />
 </picture>
</a>

<a href="#readme">
    <img src="https://img.shields.io/badge/-返回顶部-7d09f1.svg" alt="#" align="right">
</a>


## 使用协议
本仓库遵循 [AGPL-3.0 开源协议](./LICENSE)。

1. 允许个人使用，如果需要商业使用，请联系作者。
2. 除非获得商业授权，否则无论以何种方式修改或者使用代码，都需要开源，并保留相关版权信息。
3. 详细内容请参见 [AGPL-3.0 开源协议](./LICENSE)。
4. 联系请加上面的小助手，或者在 GitHub 上提 issue。
