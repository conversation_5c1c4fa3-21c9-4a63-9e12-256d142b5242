{"name": "strip-ansi", "version": "6.0.1", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": "chalk/strip-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^5.0.1"}, "devDependencies": {"ava": "^2.4.0", "tsd": "^0.10.0", "xo": "^0.25.3"}}